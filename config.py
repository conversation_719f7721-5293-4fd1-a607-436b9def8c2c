# -*- coding: utf-8 -*-
"""
إعدادات التطبيق لنظام محاسبي
Configuration settings for Mohasabi Accounting System
"""

import os
from datetime import timedelta

class Config:
    """الإعدادات الأساسية"""
    
    # إعدادات التطبيق الأساسية
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'mohasabi-secret-key-2024-arabic-accounting'
    
    # إعدادات قاعدة البيانات
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///mohasabi.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }
    
    # إعدادات الجلسات
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)
    SESSION_COOKIE_SECURE = False  # True في الإنتاج مع HTTPS
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # إعدادات الأمان
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # ساعة واحدة
    
    # إعدادات رفع الملفات
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16 ميجابايت
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    ALLOWED_EXTENSIONS = {'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 'xls', 'xlsx'}
    
    # إعدادات البريد الإلكتروني
    MAIL_SERVER = os.environ.get('MAIL_SERVER') or 'smtp.gmail.com'
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER') or '<EMAIL>'
    
    # إعدادات التطبيق
    APP_NAME = 'محاسبي'
    APP_VERSION = '1.0.0'
    APP_DESCRIPTION = 'نظام المحاسبة العربي الشامل'
    
    # إعدادات اللغة والمنطقة
    LANGUAGES = {
        'ar': 'العربية',
        'en': 'English'
    }
    DEFAULT_LANGUAGE = 'ar'
    BABEL_DEFAULT_LOCALE = 'ar'
    BABEL_DEFAULT_TIMEZONE = 'Asia/Riyadh'
    
    # إعدادات العملات المدعومة
    SUPPORTED_CURRENCIES = {
        'SAR': {'name': 'ريال سعودي', 'symbol': 'ر.س', 'decimal_places': 2},
        'AED': {'name': 'درهم إماراتي', 'symbol': 'د.إ', 'decimal_places': 2},
        'USD': {'name': 'دولار أمريكي', 'symbol': '$', 'decimal_places': 2},
        'EUR': {'name': 'يورو', 'symbol': '€', 'decimal_places': 2},
    }
    DEFAULT_CURRENCY = 'SAR'
    
    # إعدادات الضرائب
    DEFAULT_TAX_RATE = 15.00  # ضريبة القيمة المضافة في السعودية
    TAX_RATES = {
        'SAR': 15.00,  # السعودية
        'AED': 5.00,   # الإمارات
        'USD': 0.00,   # بدون ضريبة للدولار
        'EUR': 20.00,  # متوسط ضريبة أوروبا
    }
    
    # إعدادات النسخ الاحتياطية
    BACKUP_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backups')
    AUTO_BACKUP_ENABLED = True
    BACKUP_RETENTION_DAYS = 30
    
    # إعدادات التقارير
    REPORTS_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'reports')
    PDF_FONT_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'fonts')
    
    # إعدادات السجلات
    LOG_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
    LOG_LEVEL = 'INFO'
    
    @staticmethod
    def init_app(app):
        """تهيئة التطبيق"""
        # إنشاء المجلدات المطلوبة
        folders = [
            Config.UPLOAD_FOLDER,
            Config.BACKUP_FOLDER,
            Config.REPORTS_FOLDER,
            Config.LOG_FOLDER
        ]
        
        for folder in folders:
            if not os.path.exists(folder):
                os.makedirs(folder)

class DevelopmentConfig(Config):
    """إعدادات التطوير"""
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or 'sqlite:///mohasabi_dev.db'

class TestingConfig(Config):
    """إعدادات الاختبار"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('TEST_DATABASE_URL') or 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

class ProductionConfig(Config):
    """إعدادات الإنتاج"""
    DEBUG = False
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'postgresql://user:pass@localhost/mohasabi'
    SESSION_COOKIE_SECURE = True
    
    # إعدادات أمان إضافية للإنتاج
    PREFERRED_URL_SCHEME = 'https'
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # إعداد السجلات للإنتاج
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not app.debug:
            if not os.path.exists(cls.LOG_FOLDER):
                os.mkdir(cls.LOG_FOLDER)
            
            file_handler = RotatingFileHandler(
                os.path.join(cls.LOG_FOLDER, 'mohasabi.log'),
                maxBytes=10240000,  # 10 ميجابايت
                backupCount=10
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            
            app.logger.setLevel(logging.INFO)
            app.logger.info('Mohasabi startup')

# خريطة الإعدادات
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

# إعدادات إضافية للتطبيق
class AppConstants:
    """ثوابت التطبيق"""
    
    # حالات الفواتير
    INVOICE_STATUSES = {
        'draft': 'مسودة',
        'sent': 'مرسلة',
        'paid': 'مدفوعة',
        'overdue': 'متأخرة',
        'cancelled': 'ملغية'
    }
    
    # أدوار المستخدمين
    USER_ROLES = {
        'admin': 'مدير النظام',
        'accountant': 'محاسب',
        'cashier': 'أمين صندوق',
        'viewer': 'مشاهد'
    }
    
    # أنواع المعاملات
    TRANSACTION_TYPES = {
        'income': 'إيراد',
        'expense': 'مصروف'
    }
    
    # أنواع المستندات
    DOCUMENT_TYPES = {
        'invoice': 'فاتورة',
        'receipt': 'إيصال',
        'contract': 'عقد',
        'other': 'أخرى'
    }
    
    # حالات المشاريع
    PROJECT_STATUSES = {
        'active': 'نشط',
        'completed': 'مكتمل',
        'cancelled': 'ملغي'
    }

# إعدادات التحقق من البيانات
class ValidationRules:
    """قواعد التحقق من البيانات"""
    
    # قواعد كلمات المرور
    PASSWORD_MIN_LENGTH = 8
    PASSWORD_REQUIRE_UPPERCASE = True
    PASSWORD_REQUIRE_LOWERCASE = True
    PASSWORD_REQUIRE_NUMBERS = True
    PASSWORD_REQUIRE_SPECIAL = False
    
    # قواعد أرقام الهواتف السعودية
    SAUDI_PHONE_PATTERNS = [
        r'^05\d{8}$',           # 05xxxxxxxx
        r'^\+9665\d{8}$',       # +9665xxxxxxxx
        r'^9665\d{8}$',         # 9665xxxxxxxx
        r'^5\d{8}$'             # 5xxxxxxxx
    ]
    
    # قواعد الأرقام الضريبية السعودية
    SAUDI_TAX_NUMBER_PATTERN = r'^3\d{13}3$'  # يبدأ وينتهي بـ 3 ويحتوي على 15 رقم
    
    # حدود الملفات
    MAX_FILE_SIZE = 16 * 1024 * 1024  # 16 ميجابايت
    ALLOWED_IMAGE_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
    ALLOWED_DOCUMENT_EXTENSIONS = {'pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt'}
    
    # حدود النصوص
    MAX_NAME_LENGTH = 100
    MAX_DESCRIPTION_LENGTH = 500
    MAX_NOTES_LENGTH = 1000
