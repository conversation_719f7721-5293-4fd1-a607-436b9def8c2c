# -*- coding: utf-8 -*-
"""
التطبيق الرئيسي لنظام محاسبي
Main application for Mohasabi Accounting System
"""

import os
from datetime import datetime
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import LoginManager, login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash

# استيراد الإعدادات والنماذج
from config import config, AppConstants
from models import db, User, Customer, Supplier, Invoice, Transaction, Expense, init_default_data
from utils import log_user_activity, get_client_ip

def create_app(config_name=None):
    """إنشاء تطبيق Flask"""
    
    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG', 'development')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # تهيئة قاعدة البيانات
    db.init_app(app)
    
    # تهيئة نظام تسجيل الدخول
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # إنشاء قاعدة البيانات والبيانات الافتراضية
    with app.app_context():
        db.create_all()
        init_default_data()
    
    # تسجيل المسارات
    register_blueprints(app)
    register_error_handlers(app)
    register_template_filters(app)
    
    return app

def register_blueprints(app):
    """تسجيل مسارات التطبيق"""
    
    # المسار الرئيسي
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return redirect(url_for('auth.login'))
    
    # لوحة التحكم
    @app.route('/dashboard')
    @login_required
    def dashboard():
        # إحصائيات سريعة
        stats = {
            'total_customers': Customer.query.filter_by(is_active=True).count(),
            'total_suppliers': Supplier.query.filter_by(is_active=True).count(),
            'total_invoices': Invoice.query.count(),
            'pending_invoices': Invoice.query.filter_by(status='sent').count(),
            'overdue_invoices': Invoice.query.filter(
                Invoice.due_date < datetime.now().date(),
                Invoice.status.in_(['sent', 'overdue'])
            ).count(),
        }
        
        # آخر الفواتير
        recent_invoices = Invoice.query.order_by(Invoice.created_at.desc()).limit(5).all()
        
        # آخر المعاملات
        recent_transactions = Transaction.query.order_by(Transaction.created_at.desc()).limit(5).all()
        
        return render_template('dashboard.html', 
                             stats=stats,
                             recent_invoices=recent_invoices,
                             recent_transactions=recent_transactions)
    
    # تسجيل مسارات المصادقة
    register_auth_routes(app)
    
    # تسجيل مسارات العملاء
    register_customer_routes(app)
    
    # تسجيل مسارات الفواتير
    register_invoice_routes(app)

def register_auth_routes(app):
    """تسجيل مسارات المصادقة"""
    
    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        
        if request.method == 'POST':
            username = request.form.get('username')
            password = request.form.get('password')
            remember = bool(request.form.get('remember'))
            
            if not username or not password:
                flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')
                return render_template('auth/login.html')
            
            user = User.query.filter_by(username=username).first()
            
            if user and user.check_password(password):
                if not user.is_active:
                    flash('حسابك غير نشط. يرجى التواصل مع المدير', 'error')
                    return render_template('auth/login.html')
                
                login_user(user, remember=remember)
                user.last_login = datetime.utcnow()
                db.session.commit()
                
                # تسجيل نشاط تسجيل الدخول
                log_user_activity(user.id, 'login', f'تسجيل دخول من {get_client_ip(request)}')
                
                flash(f'مرحباً {user.full_name}', 'success')
                
                # إعادة التوجيه للصفحة المطلوبة أو لوحة التحكم
                next_page = request.args.get('next')
                return redirect(next_page) if next_page else redirect(url_for('dashboard'))
            else:
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
        
        return render_template('auth/login.html')
    
    @app.route('/logout')
    @login_required
    def logout():
        # تسجيل نشاط تسجيل الخروج
        log_user_activity(current_user.id, 'logout', f'تسجيل خروج من {get_client_ip(request)}')
        
        logout_user()
        flash('تم تسجيل الخروج بنجاح', 'info')
        return redirect(url_for('login'))

def register_customer_routes(app):
    """تسجيل مسارات العملاء"""
    
    @app.route('/customers')
    @login_required
    def customers():
        page = request.args.get('page', 1, type=int)
        search = request.args.get('search', '')
        
        query = Customer.query
        
        if search:
            query = query.filter(
                Customer.name.contains(search) |
                Customer.email.contains(search) |
                Customer.phone.contains(search)
            )
        
        customers = query.order_by(Customer.name).paginate(
            page=page, per_page=20, error_out=False
        )
        
        return render_template('customers/list.html', customers=customers, search=search)
    
    @app.route('/customers/add', methods=['GET', 'POST'])
    @login_required
    def add_customer():
        if not current_user.has_permission('create'):
            flash('ليس لديك صلاحية لإضافة عملاء جدد', 'error')
            return redirect(url_for('customers'))
        
        if request.method == 'POST':
            try:
                customer = Customer(
                    name=request.form.get('name'),
                    email=request.form.get('email') or None,
                    phone=request.form.get('phone') or None,
                    tax_number=request.form.get('tax_number') or None,
                    address=request.form.get('address') or None,
                    credit_limit=float(request.form.get('credit_limit', 0)),
                    is_active=bool(int(request.form.get('is_active', 1))),
                    notes=request.form.get('notes') or None
                )
                
                db.session.add(customer)
                db.session.commit()
                
                # تسجيل النشاط
                log_user_activity(current_user.id, 'create', f'إضافة عميل جديد: {customer.name}')
                
                flash(f'تم إضافة العميل {customer.name} بنجاح', 'success')
                
                # التحقق من الرغبة في إضافة عميل آخر
                if request.form.get('save_and_new'):
                    return redirect(url_for('add_customer'))
                else:
                    return redirect(url_for('customers'))
                    
            except Exception as e:
                db.session.rollback()
                flash('حدث خطأ في إضافة العميل', 'error')
                app.logger.error(f'Error adding customer: {str(e)}')
        
        return render_template('customers/add.html')
    
    @app.route('/customers/<int:customer_id>')
    @login_required
    def view_customer(customer_id):
        customer = Customer.query.get_or_404(customer_id)
        
        # آخر الفواتير للعميل
        recent_invoices = Invoice.query.filter_by(customer_id=customer_id)\
                                     .order_by(Invoice.created_at.desc())\
                                     .limit(10).all()
        
        # آخر المعاملات للعميل
        recent_transactions = Transaction.query.filter_by(customer_id=customer_id)\
                                             .order_by(Transaction.created_at.desc())\
                                             .limit(10).all()
        
        return render_template('customers/view.html', 
                             customer=customer,
                             recent_invoices=recent_invoices,
                             recent_transactions=recent_transactions)

def register_invoice_routes(app):
    """تسجيل مسارات الفواتير"""
    
    @app.route('/invoices')
    @login_required
    def invoices():
        page = request.args.get('page', 1, type=int)
        status = request.args.get('status', '')
        
        query = Invoice.query
        
        if status:
            query = query.filter_by(status=status)
        
        invoices = query.order_by(Invoice.created_at.desc()).paginate(
            page=page, per_page=20, error_out=False
        )
        
        return render_template('invoices/list.html', invoices=invoices, status=status)
    
    @app.route('/invoices/add')
    @login_required
    def add_invoice():
        if not current_user.has_permission('create'):
            flash('ليس لديك صلاحية لإنشاء فواتير جديدة', 'error')
            return redirect(url_for('invoices'))
        
        customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
        return render_template('invoices/add.html', customers=customers)

def register_error_handlers(app):
    """تسجيل معالجات الأخطاء"""
    
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500
    
    @app.errorhandler(403)
    def forbidden_error(error):
        return render_template('errors/403.html'), 403

def register_template_filters(app):
    """تسجيل مرشحات القوالب"""
    
    @app.template_filter('currency')
    def currency_filter(amount, currency='SAR'):
        from utils import format_currency
        return format_currency(amount, currency)
    
    @app.template_filter('phone')
    def phone_filter(phone):
        from utils import format_phone_number
        return format_phone_number(phone)
    
    @app.template_filter('status_badge')
    def status_badge_filter(status):
        badges = {
            'draft': 'secondary',
            'sent': 'info',
            'paid': 'success',
            'overdue': 'danger',
            'cancelled': 'dark'
        }
        return badges.get(status, 'secondary')
    
    @app.template_global()
    def get_constants():
        return AppConstants

# إنشاء التطبيق
app = create_app()

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
