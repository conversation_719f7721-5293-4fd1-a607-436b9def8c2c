# -*- coding: utf-8 -*-
"""
وظائف مساعدة لنظام محاسبي
Utility functions for Mohasabi Accounting System
"""

import re
import os
import uuid
import json
import hashlib
from datetime import datetime, date
from decimal import Decimal
from typing import Union, Optional, Dict, Any
from werkzeug.utils import secure_filename
from flask import current_app

def generate_invoice_number(prefix: str = "INV-") -> str:
    """توليد رقم فاتورة فريد"""
    from models import Invoice, db
    
    # الحصول على آخر رقم فاتورة
    last_invoice = Invoice.query.order_by(Invoice.id.desc()).first()
    
    if last_invoice and last_invoice.invoice_number.startswith(prefix):
        try:
            # استخراج الرقم من آخر فاتورة
            last_number = int(last_invoice.invoice_number.replace(prefix, ''))
            new_number = last_number + 1
        except ValueError:
            new_number = 1
    else:
        new_number = 1
    
    return f"{prefix}{new_number:06d}"

def validate_saudi_phone(phone: str) -> bool:
    """التحقق من صحة رقم الهاتف السعودي"""
    if not phone:
        return True  # اختياري
    
    # إزالة المسافات والرموز
    phone = re.sub(r'[\s\-\(\)]', '', phone)
    
    # الأنماط المقبولة
    patterns = [
        r'^05\d{8}$',           # 05xxxxxxxx
        r'^\+9665\d{8}$',       # +9665xxxxxxxx
        r'^9665\d{8}$',         # 9665xxxxxxxx
        r'^5\d{8}$'             # 5xxxxxxxx
    ]
    
    return any(re.match(pattern, phone) for pattern in patterns)

def validate_saudi_tax_number(tax_number: str) -> bool:
    """التحقق من صحة الرقم الضريبي السعودي"""
    if not tax_number:
        return True  # اختياري
    
    # يجب أن يكون 15 رقم ويبدأ وينتهي بـ 3
    pattern = r'^3\d{13}3$'
    return bool(re.match(pattern, tax_number))

def format_currency(amount: Union[float, Decimal, int], currency: str = 'SAR') -> str:
    """تنسيق العملة"""
    if amount is None:
        amount = 0
    
    # تحويل إلى Decimal للدقة
    if not isinstance(amount, Decimal):
        amount = Decimal(str(amount))
    
    # رموز العملات
    currency_symbols = {
        'SAR': 'ر.س',
        'AED': 'د.إ',
        'USD': '$',
        'EUR': '€'
    }
    
    symbol = currency_symbols.get(currency, currency)
    
    # تنسيق الرقم مع الفواصل
    formatted = f"{amount:,.2f}"
    
    return f"{formatted} {symbol}"

def format_phone_number(phone: str) -> str:
    """تنسيق رقم الهاتف"""
    if not phone:
        return ""
    
    # إزالة جميع الرموز
    phone = re.sub(r'[\s\-\(\)]', '', phone)
    
    # تنسيق الرقم السعودي
    if phone.startswith('+966'):
        return phone
    elif phone.startswith('966'):
        return f"+{phone}"
    elif phone.startswith('05'):
        return f"+966{phone[1:]}"
    elif phone.startswith('5') and len(phone) == 9:
        return f"+966{phone}"
    
    return phone

def numbers_to_arabic_words(number: Union[int, float, Decimal]) -> str:
    """تحويل الأرقام إلى كلمات عربية"""
    
    if number == 0:
        return "صفر"
    
    # تحويل إلى عدد صحيح للتبسيط
    number = int(number)
    
    # الأرقام الأساسية
    ones = ["", "واحد", "اثنان", "ثلاثة", "أربعة", "خمسة", "ستة", "سبعة", "ثمانية", "تسعة"]
    tens = ["", "", "عشرون", "ثلاثون", "أربعون", "خمسون", "ستون", "سبعون", "ثمانون", "تسعون"]
    teens = ["عشرة", "أحد عشر", "اثنا عشر", "ثلاثة عشر", "أربعة عشر", "خمسة عشر", 
             "ستة عشر", "سبعة عشر", "ثمانية عشر", "تسعة عشر"]
    hundreds = ["", "مائة", "مائتان", "ثلاثمائة", "أربعمائة", "خمسمائة", 
                "ستمائة", "سبعمائة", "ثمانمائة", "تسعمائة"]
    
    def convert_hundreds(n):
        result = []
        
        # المئات
        if n >= 100:
            h = n // 100
            result.append(hundreds[h])
            n %= 100
        
        # العشرات والآحاد
        if n >= 20:
            t = n // 10
            result.append(tens[t])
            n %= 10
            if n > 0:
                result.append(ones[n])
        elif n >= 10:
            result.append(teens[n - 10])
        elif n > 0:
            result.append(ones[n])
        
        return " ".join(result)
    
    if number < 1000:
        return convert_hundreds(number)
    elif number < 1000000:
        thousands = number // 1000
        remainder = number % 1000
        
        result = []
        if thousands == 1:
            result.append("ألف")
        elif thousands == 2:
            result.append("ألفان")
        elif thousands < 11:
            result.append(f"{convert_hundreds(thousands)} آلاف")
        else:
            result.append(f"{convert_hundreds(thousands)} ألف")
        
        if remainder > 0:
            result.append(convert_hundreds(remainder))
        
        return " ".join(result)
    else:
        return "رقم كبير جداً"

def allowed_file(filename: str) -> bool:
    """التحقق من امتداد الملف المسموح"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

def save_uploaded_file(file, folder: str = 'documents') -> Optional[Dict[str, Any]]:
    """حفظ الملف المرفوع"""
    if not file or not allowed_file(file.filename):
        return None
    
    # إنشاء اسم ملف آمن
    original_filename = file.filename
    filename = secure_filename(original_filename)
    
    # إضافة UUID لتجنب التضارب
    name, ext = os.path.splitext(filename)
    unique_filename = f"{name}_{uuid.uuid4().hex[:8]}{ext}"
    
    # إنشاء مجلد الحفظ
    upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], folder)
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder)
    
    # حفظ الملف
    file_path = os.path.join(upload_folder, unique_filename)
    file.save(file_path)
    
    # حساب حجم الملف
    file_size = os.path.getsize(file_path)
    
    return {
        'filename': unique_filename,
        'original_filename': original_filename,
        'file_path': file_path,
        'file_size': file_size,
        'mime_type': file.content_type
    }

def calculate_tax(amount: Decimal, tax_rate: Decimal) -> Decimal:
    """حساب الضريبة"""
    if not amount or not tax_rate:
        return Decimal('0')
    
    return (amount * tax_rate / 100).quantize(Decimal('0.01'))

def get_hijri_date(gregorian_date: date = None) -> str:
    """تحويل التاريخ الميلادي إلى هجري (تقريبي)"""
    if not gregorian_date:
        gregorian_date = date.today()
    
    # تحويل تقريبي (يحتاج مكتبة متخصصة للدقة)
    # هذا مجرد مثال بسيط
    hijri_year = gregorian_date.year - 579
    
    months = [
        "محرم", "صفر", "ربيع الأول", "ربيع الثاني", "جمادى الأولى", "جمادى الثانية",
        "رجب", "شعبان", "رمضان", "شوال", "ذو القعدة", "ذو الحجة"
    ]
    
    month_name = months[gregorian_date.month - 1]
    
    return f"{gregorian_date.day} {month_name} {hijri_year}هـ"

def generate_file_hash(file_path: str) -> str:
    """توليد hash للملف للتحقق من التكامل"""
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def format_file_size(size_bytes: int) -> str:
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0 بايت"
    
    size_names = ["بايت", "كيلوبايت", "ميجابايت", "جيجابايت"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def sanitize_filename(filename: str) -> str:
    """تنظيف اسم الملف من الأحرف غير المرغوبة"""
    # إزالة الأحرف الخاصة والمسافات
    filename = re.sub(r'[^\w\s\-_\.]', '', filename)
    filename = re.sub(r'[\s]+', '_', filename)
    return filename

def validate_email(email: str) -> bool:
    """التحقق من صحة البريد الإلكتروني"""
    if not email:
        return True  # اختياري
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def get_client_ip(request) -> str:
    """الحصول على عنوان IP للعميل"""
    if request.environ.get('HTTP_X_FORWARDED_FOR') is None:
        return request.environ['REMOTE_ADDR']
    else:
        return request.environ['HTTP_X_FORWARDED_FOR']

def log_user_activity(user_id: int, action: str, details: str = None):
    """تسجيل نشاط المستخدم"""
    from models import AuditLog, db
    from flask import request
    
    log = AuditLog(
        user_id=user_id,
        action=action,
        new_values=details,
        ip_address=get_client_ip(request),
        user_agent=request.headers.get('User-Agent')
    )
    
    db.session.add(log)
    db.session.commit()

def create_backup_filename() -> str:
    """إنشاء اسم ملف النسخة الاحتياطية"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    return f"mohasabi_backup_{timestamp}.sql"

def is_business_day(check_date: date = None) -> bool:
    """التحقق من كون اليوم يوم عمل (ليس جمعة أو سبت)"""
    if not check_date:
        check_date = date.today()
    
    # في السعودية: الجمعة = 4، السبت = 5
    return check_date.weekday() not in [4, 5]
