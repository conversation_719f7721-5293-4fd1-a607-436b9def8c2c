{% extends "base.html" %}

{% block title %}العملاء - محاسبي{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>
        إدارة العملاء
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportCustomers()">
                <i class="fas fa-download me-1"></i>
                تصدير
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-print me-1"></i>
                طباعة
            </button>
        </div>
        {% if current_user.has_permission('create') %}
        <a href="{{ url_for('add_customer') }}" class="btn btn-sm btn-primary">
            <i class="fas fa-plus me-1"></i>
            عميل جديد
        </a>
        {% endif %}
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" name="search" 
                           placeholder="البحث في الاسم، البريد الإلكتروني، أو الهاتف..." 
                           value="{{ search }}">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                </select>
            </div>
            <div class="col-md-3">
                <div class="btn-group w-100">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                    <a href="{{ url_for('customers') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Customers Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            قائمة العملاء
            {% if customers.total %}
                <span class="badge bg-primary">{{ customers.total }}</span>
            {% endif %}
        </h5>
        <div class="btn-group btn-group-sm">
            <button type="button" class="btn btn-outline-secondary" onclick="selectAll()">
                تحديد الكل
            </button>
            <button type="button" class="btn btn-outline-danger" onclick="deleteSelected()" disabled id="deleteBtn">
                حذف المحدد
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        {% if customers.items %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="50">
                                <input type="checkbox" class="form-check-input" id="selectAllCheckbox">
                            </th>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الهاتف</th>
                            <th>الرقم الضريبي</th>
                            <th>الحد الائتماني</th>
                            <th>الحالة</th>
                            <th>تاريخ الإضافة</th>
                            <th width="120">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in customers.items %}
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input customer-checkbox" 
                                       value="{{ customer.id }}">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar me-2" style="width: 35px; height: 35px;">
                                        {{ customer.name[0] }}
                                    </div>
                                    <div>
                                        <a href="{{ url_for('view_customer', customer_id=customer.id) }}" 
                                           class="text-decoration-none fw-bold">
                                            {{ customer.name }}
                                        </a>
                                        {% if customer.notes %}
                                            <br><small class="text-muted">{{ customer.notes[:50] }}{% if customer.notes|length > 50 %}...{% endif %}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if customer.email %}
                                    <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                        {{ customer.email }}
                                    </a>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if customer.phone %}
                                    <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                        {{ customer.phone|phone }}
                                    </a>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if customer.tax_number %}
                                    <code>{{ customer.tax_number }}</code>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if customer.credit_limit > 0 %}
                                    <span class="text-success">{{ customer.credit_limit|currency }}</span>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if customer.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                {% else %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ customer.created_at.strftime('%Y-%m-%d') }}
                                </small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('view_customer', customer_id=customer.id) }}" 
                                       class="btn btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if current_user.has_permission('update') %}
                                    <a href="#" class="btn btn-outline-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                    {% if current_user.has_permission('delete') %}
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="deleteCustomer({{ customer.id }}, '{{ customer.name }}')" 
                                            title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if customers.pages > 1 %}
            <div class="card-footer">
                <nav aria-label="تنقل الصفحات">
                    <ul class="pagination justify-content-center mb-0">
                        {% if customers.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('customers', page=customers.prev_num, search=search) }}">
                                    السابق
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in customers.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != customers.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('customers', page=page_num, search=search) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if customers.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('customers', page=customers.next_num, search=search) }}">
                                    التالي
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                
                <div class="text-center mt-2">
                    <small class="text-muted">
                        عرض {{ customers.per_page * (customers.page - 1) + 1 }} إلى 
                        {{ customers.per_page * customers.page if customers.page < customers.pages else customers.total }} 
                        من أصل {{ customers.total }} عميل
                    </small>
                </div>
            </div>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد عملاء</h5>
                {% if search %}
                    <p class="text-muted">لم يتم العثور على عملاء مطابقين لبحثك</p>
                    <a href="{{ url_for('customers') }}" class="btn btn-outline-primary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء البحث
                    </a>
                {% else %}
                    <p class="text-muted">ابدأ بإضافة عملائك الأوائل</p>
                    {% if current_user.has_permission('create') %}
                    <a href="{{ url_for('add_customer') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        إضافة عميل جديد
                    </a>
                    {% endif %}
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Select all functionality
    function selectAll() {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const customerCheckboxes = document.querySelectorAll('.customer-checkbox');
        
        selectAllCheckbox.checked = !selectAllCheckbox.checked;
        customerCheckboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });
        
        updateDeleteButton();
    }
    
    // Update delete button state
    function updateDeleteButton() {
        const checkedBoxes = document.querySelectorAll('.customer-checkbox:checked');
        const deleteBtn = document.getElementById('deleteBtn');
        
        deleteBtn.disabled = checkedBoxes.length === 0;
        deleteBtn.textContent = checkedBoxes.length > 0 ? 
            `حذف المحدد (${checkedBoxes.length})` : 'حذف المحدد';
    }
    
    // Listen for checkbox changes
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('customer-checkbox')) {
            updateDeleteButton();
            
            // Update select all checkbox
            const customerCheckboxes = document.querySelectorAll('.customer-checkbox');
            const checkedBoxes = document.querySelectorAll('.customer-checkbox:checked');
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            
            selectAllCheckbox.checked = checkedBoxes.length === customerCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < customerCheckboxes.length;
        }
    });
    
    // Delete single customer
    function deleteCustomer(customerId, customerName) {
        if (MohasabiApp.confirmDelete(`هل أنت متأكد من حذف العميل "${customerName}"؟`)) {
            MohasabiApp.showLoading();
            
            fetch(`/customers/${customerId}/delete`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken()
                }
            })
            .then(response => response.json())
            .then(data => {
                MohasabiApp.hideLoading();
                if (data.success) {
                    MohasabiApp.showAlert('تم حذف العميل بنجاح', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    MohasabiApp.showAlert(data.message || 'حدث خطأ في الحذف', 'error');
                }
            })
            .catch(error => {
                MohasabiApp.hideLoading();
                MohasabiApp.showAlert('حدث خطأ في الاتصال', 'error');
            });
        }
    }
    
    // Delete selected customers
    function deleteSelected() {
        const checkedBoxes = document.querySelectorAll('.customer-checkbox:checked');
        const customerIds = Array.from(checkedBoxes).map(cb => cb.value);
        
        if (customerIds.length === 0) return;
        
        if (MohasabiApp.confirmDelete(`هل أنت متأكد من حذف ${customerIds.length} عميل؟`)) {
            MohasabiApp.showLoading();
            
            fetch('/customers/delete-multiple', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken()
                },
                body: JSON.stringify({ customer_ids: customerIds })
            })
            .then(response => response.json())
            .then(data => {
                MohasabiApp.hideLoading();
                if (data.success) {
                    MohasabiApp.showAlert(`تم حذف ${data.deleted_count} عميل بنجاح`, 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    MohasabiApp.showAlert(data.message || 'حدث خطأ في الحذف', 'error');
                }
            })
            .catch(error => {
                MohasabiApp.hideLoading();
                MohasabiApp.showAlert('حدث خطأ في الاتصال', 'error');
            });
        }
    }
    
    // Export customers
    function exportCustomers() {
        const searchParam = new URLSearchParams(window.location.search).get('search') || '';
        const exportUrl = `/customers/export?search=${encodeURIComponent(searchParam)}`;
        
        MohasabiApp.showLoading();
        window.location.href = exportUrl;
        
        setTimeout(() => {
            MohasabiApp.hideLoading();
        }, 2000);
    }
</script>
{% endblock %}
