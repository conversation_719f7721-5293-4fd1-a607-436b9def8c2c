{% extends "base.html" %}

{% block title %}لوحة التحكم - محاسبي{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>
        لوحة التحكم
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-download me-1"></i>
                تصدير
            </button>
        </div>
        <button type="button" class="btn btn-sm btn-primary">
            <i class="fas fa-plus me-1"></i>
            إضافة جديد
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="h4 mb-0">{{ stats.total_customers }}</div>
                    <div class="small">إجمالي العملاء</div>
                </div>
                <div class="ms-3">
                    <i class="fas fa-users fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card success">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="h4 mb-0">{{ stats.total_invoices }}</div>
                    <div class="small">إجمالي الفواتير</div>
                </div>
                <div class="ms-3">
                    <i class="fas fa-file-invoice fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card warning">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="h4 mb-0">{{ stats.pending_invoices }}</div>
                    <div class="small">فواتير معلقة</div>
                </div>
                <div class="ms-3">
                    <i class="fas fa-clock fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card info">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <div class="h4 mb-0">{{ stats.overdue_invoices }}</div>
                    <div class="small">فواتير متأخرة</div>
                </div>
                <div class="ms-3">
                    <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('add_invoice') }}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                            <i class="fas fa-plus-circle fa-2x mb-2"></i>
                            <span>فاتورة جديدة</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('add_customer') }}" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                            <i class="fas fa-user-plus fa-2x mb-2"></i>
                            <span>عميل جديد</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                            <i class="fas fa-receipt fa-2x mb-2"></i>
                            <span>مصروف جديد</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i>
                            <span>تقرير مالي</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <!-- Recent Invoices -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-invoice me-2"></i>
                    آخر الفواتير
                </h5>
                <a href="{{ url_for('invoices') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if recent_invoices %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in recent_invoices %}
                                <tr>
                                    <td>
                                        <a href="#" class="text-decoration-none">
                                            {{ invoice.invoice_number }}
                                        </a>
                                    </td>
                                    <td>{{ invoice.customer.name if invoice.customer else 'غير محدد' }}</td>
                                    <td>{{ invoice.total_amount|currency }}</td>
                                    <td>
                                        <span class="badge bg-{{ invoice.status|status_badge }}">
                                            {{ get_constants().INVOICE_STATUSES[invoice.status] }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد فواتير حتى الآن</p>
                        <a href="{{ url_for('add_invoice') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            إنشاء فاتورة جديدة
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Recent Transactions -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exchange-alt me-2"></i>
                    آخر المعاملات
                </h5>
                <a href="#" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if recent_transactions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الوصف</th>
                                    <th>النوع</th>
                                    <th>المبلغ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions %}
                                <tr>
                                    <td>{{ transaction.transaction_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ transaction.description[:30] }}{% if transaction.description|length > 30 %}...{% endif %}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if transaction.type == 'income' else 'danger' }}">
                                            {{ get_constants().TRANSACTION_TYPES[transaction.type] }}
                                        </span>
                                    </td>
                                    <td class="{{ 'text-success' if transaction.type == 'income' else 'text-danger' }}">
                                        {{ '+' if transaction.type == 'income' else '-' }}{{ transaction.amount|currency }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد معاملات حتى الآن</p>
                        <a href="#" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            إضافة معاملة جديدة
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    نظرة عامة على الأداء
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-8">
                        <canvas id="revenueChart" width="400" height="200"></canvas>
                    </div>
                    <div class="col-lg-4">
                        <div class="d-flex flex-column h-100 justify-content-center">
                            <div class="text-center mb-3">
                                <h6 class="text-muted">الإيرادات هذا الشهر</h6>
                                <h3 class="text-success">0.00 ر.س</h3>
                            </div>
                            <div class="text-center mb-3">
                                <h6 class="text-muted">المصروفات هذا الشهر</h6>
                                <h3 class="text-danger">0.00 ر.س</h3>
                            </div>
                            <div class="text-center">
                                <h6 class="text-muted">صافي الربح</h6>
                                <h3 class="text-primary">0.00 ر.س</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Revenue Chart
    const ctx = document.getElementById('revenueChart').getContext('2d');
    const revenueChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'الإيرادات',
                data: [0, 0, 0, 0, 0, 0],
                borderColor: '#2c5aa0',
                backgroundColor: 'rgba(44, 90, 160, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'المصروفات',
                data: [0, 0, 0, 0, 0, 0],
                borderColor: '#dc3545',
                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: true,
                    text: 'الإيرادات والمصروفات الشهرية'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString('ar-SA') + ' ر.س';
                        }
                    }
                }
            }
        }
    });
    
    // Auto-refresh dashboard every 5 minutes
    setInterval(function() {
        // You can add AJAX calls here to refresh data
        console.log('Dashboard auto-refresh');
    }, 300000); // 5 minutes
</script>
{% endblock %}
