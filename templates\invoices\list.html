{% extends "base.html" %}

{% block title %}الفواتير - محاسبي{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-file-invoice me-2"></i>
        إدارة الفواتير
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportInvoices()">
                <i class="fas fa-download me-1"></i>
                تصدير
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-print me-1"></i>
                طباعة
            </button>
        </div>
        {% if current_user.has_permission('create') %}
        <a href="{{ url_for('add_invoice') }}" class="btn btn-sm btn-primary">
            <i class="fas fa-plus me-1"></i>
            فاتورة جديدة
        </a>
        {% endif %}
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="h4 mb-0">{{ invoices.total if invoices.items else 0 }}</div>
                        <div class="small">إجمالي الفواتير</div>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-file-invoice fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="h4 mb-0">0</div>
                        <div class="small">فواتير مدفوعة</div>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="h4 mb-0">0</div>
                        <div class="small">فواتير معلقة</div>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="h4 mb-0">0</div>
                        <div class="small">فواتير متأخرة</div>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" name="search" 
                           placeholder="البحث في رقم الفاتورة أو اسم العميل..." 
                           value="{{ request.args.get('search', '') }}">
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" name="status">
                    <option value="">جميع الحالات</option>
                    {% for key, value in get_constants().INVOICE_STATUSES.items() %}
                        <option value="{{ key }}" {{ 'selected' if request.args.get('status') == key }}>
                            {{ value }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <input type="date" class="form-control" name="date_from" 
                       value="{{ request.args.get('date_from', '') }}" placeholder="من تاريخ">
            </div>
            <div class="col-md-2">
                <input type="date" class="form-control" name="date_to" 
                       value="{{ request.args.get('date_to', '') }}" placeholder="إلى تاريخ">
            </div>
            <div class="col-md-2">
                <div class="btn-group w-100">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                    <a href="{{ url_for('invoices') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Invoices Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            قائمة الفواتير
            {% if invoices.total %}
                <span class="badge bg-primary">{{ invoices.total }}</span>
            {% endif %}
        </h5>
        <div class="btn-group btn-group-sm">
            <button type="button" class="btn btn-outline-secondary" onclick="selectAll()">
                تحديد الكل
            </button>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-primary dropdown-toggle" 
                        data-bs-toggle="dropdown" disabled id="bulkActionsBtn">
                    إجراءات جماعية
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="bulkAction('send')">
                        <i class="fas fa-paper-plane me-2"></i>إرسال
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="bulkAction('print')">
                        <i class="fas fa-print me-2"></i>طباعة
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-danger" href="#" onclick="bulkAction('delete')">
                        <i class="fas fa-trash me-2"></i>حذف
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        {% if invoices.items %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="50">
                                <input type="checkbox" class="form-check-input" id="selectAllCheckbox">
                            </th>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>تاريخ الفاتورة</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>المبلغ الإجمالي</th>
                            <th>الحالة</th>
                            <th width="120">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for invoice in invoices.items %}
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input invoice-checkbox" 
                                       value="{{ invoice.id }}">
                            </td>
                            <td>
                                <a href="#" class="text-decoration-none fw-bold">
                                    {{ invoice.invoice_number }}
                                </a>
                            </td>
                            <td>
                                {% if invoice.customer %}
                                    <a href="{{ url_for('view_customer', customer_id=invoice.customer.id) }}" 
                                       class="text-decoration-none">
                                        {{ invoice.customer.name }}
                                    </a>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                            <td>
                                {% if invoice.due_date %}
                                    {{ invoice.due_date.strftime('%Y-%m-%d') }}
                                    {% if invoice.due_date < today() and invoice.status in ['sent', 'overdue'] %}
                                        <span class="badge bg-danger ms-1">متأخر</span>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td class="fw-bold">{{ invoice.total_amount|currency }}</td>
                            <td>
                                <span class="badge bg-{{ invoice.status|status_badge }}">
                                    {{ get_constants().INVOICE_STATUSES[invoice.status] }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="#" class="btn btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if current_user.has_permission('update') %}
                                    <a href="#" class="btn btn-outline-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" 
                                                data-bs-toggle="dropdown" title="المزيد">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#">
                                                <i class="fas fa-print me-2"></i>طباعة
                                            </a></li>
                                            <li><a class="dropdown-item" href="#">
                                                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                                            </a></li>
                                            <li><a class="dropdown-item" href="#">
                                                <i class="fas fa-paper-plane me-2"></i>إرسال بالبريد
                                            </a></li>
                                            {% if invoice.status == 'sent' %}
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" href="#">
                                                <i class="fas fa-money-bill me-2"></i>تسجيل دفعة
                                            </a></li>
                                            {% endif %}
                                            {% if current_user.has_permission('delete') %}
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#" 
                                                   onclick="deleteInvoice({{ invoice.id }}, '{{ invoice.invoice_number }}')">
                                                <i class="fas fa-trash me-2"></i>حذف
                                            </a></li>
                                            {% endif %}
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if invoices.pages > 1 %}
            <div class="card-footer">
                <nav aria-label="تنقل الصفحات">
                    <ul class="pagination justify-content-center mb-0">
                        {% if invoices.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('invoices', page=invoices.prev_num, **request.args) }}">
                                    السابق
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in invoices.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != invoices.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('invoices', page=page_num, **request.args) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if invoices.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('invoices', page=invoices.next_num, **request.args) }}">
                                    التالي
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                
                <div class="text-center mt-2">
                    <small class="text-muted">
                        عرض {{ invoices.per_page * (invoices.page - 1) + 1 }} إلى 
                        {{ invoices.per_page * invoices.page if invoices.page < invoices.pages else invoices.total }} 
                        من أصل {{ invoices.total }} فاتورة
                    </small>
                </div>
            </div>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-file-invoice fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد فواتير</h5>
                {% if request.args.get('search') or request.args.get('status') %}
                    <p class="text-muted">لم يتم العثور على فواتير مطابقة لمعايير البحث</p>
                    <a href="{{ url_for('invoices') }}" class="btn btn-outline-primary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء البحث
                    </a>
                {% else %}
                    <p class="text-muted">ابدأ بإنشاء فاتورتك الأولى</p>
                    {% if current_user.has_permission('create') %}
                    <a href="{{ url_for('add_invoice') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        إنشاء فاتورة جديدة
                    </a>
                    {% endif %}
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Get today's date for comparison
    function today() {
        return new Date().toISOString().split('T')[0];
    }
    
    // Select all functionality
    function selectAll() {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const invoiceCheckboxes = document.querySelectorAll('.invoice-checkbox');
        
        selectAllCheckbox.checked = !selectAllCheckbox.checked;
        invoiceCheckboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });
        
        updateBulkActionsButton();
    }
    
    // Update bulk actions button state
    function updateBulkActionsButton() {
        const checkedBoxes = document.querySelectorAll('.invoice-checkbox:checked');
        const bulkActionsBtn = document.getElementById('bulkActionsBtn');
        
        bulkActionsBtn.disabled = checkedBoxes.length === 0;
    }
    
    // Listen for checkbox changes
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('invoice-checkbox')) {
            updateBulkActionsButton();
            
            // Update select all checkbox
            const invoiceCheckboxes = document.querySelectorAll('.invoice-checkbox');
            const checkedBoxes = document.querySelectorAll('.invoice-checkbox:checked');
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            
            selectAllCheckbox.checked = checkedBoxes.length === invoiceCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < invoiceCheckboxes.length;
        }
    });
    
    // Bulk actions
    function bulkAction(action) {
        const checkedBoxes = document.querySelectorAll('.invoice-checkbox:checked');
        const invoiceIds = Array.from(checkedBoxes).map(cb => cb.value);
        
        if (invoiceIds.length === 0) return;
        
        switch(action) {
            case 'send':
                MohasabiApp.showAlert('ميزة الإرسال الجماعي قيد التطوير', 'info');
                break;
            case 'print':
                MohasabiApp.showAlert('ميزة الطباعة الجماعية قيد التطوير', 'info');
                break;
            case 'delete':
                if (MohasabiApp.confirmDelete(`هل أنت متأكد من حذف ${invoiceIds.length} فاتورة؟`)) {
                    MohasabiApp.showAlert('ميزة الحذف الجماعي قيد التطوير', 'info');
                }
                break;
        }
    }
    
    // Delete single invoice
    function deleteInvoice(invoiceId, invoiceNumber) {
        if (MohasabiApp.confirmDelete(`هل أنت متأكد من حذف الفاتورة "${invoiceNumber}"؟`)) {
            MohasabiApp.showAlert('ميزة حذف الفاتورة قيد التطوير', 'info');
        }
    }
    
    // Export invoices
    function exportInvoices() {
        const params = new URLSearchParams(window.location.search);
        const exportUrl = `/invoices/export?${params.toString()}`;
        
        MohasabiApp.showLoading();
        window.location.href = exportUrl;
        
        setTimeout(() => {
            MohasabiApp.hideLoading();
        }, 2000);
    }
</script>
{% endblock %}
