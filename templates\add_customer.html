{% extends "base.html" %}

{% block title %}إضافة عميل جديد - محاسبي{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('customers') }}">العملاء</a></li>
                <li class="breadcrumb-item active">إضافة عميل جديد</li>
            </ol>
        </nav>
        
        <h1 class="h3 mb-4">
            <i class="fas fa-user-plus me-2"></i>
            إضافة عميل جديد
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">معلومات العميل</h6>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- اسم العميل -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">
                                <i class="fas fa-user me-1"></i>
                                اسم العميل <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="invalid-feedback">
                                يرجى إدخال اسم العميل
                            </div>
                        </div>
                        
                        <!-- البريد الإلكتروني -->
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-1"></i>
                                البريد الإلكتروني
                            </label>
                            <input type="email" class="form-control" id="email" name="email">
                            <div class="invalid-feedback">
                                يرجى إدخال بريد إلكتروني صحيح
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- رقم الهاتف -->
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone me-1"></i>
                                رقم الهاتف
                            </label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   placeholder="05xxxxxxxx">
                            <div class="form-text">مثال: 0501234567</div>
                        </div>
                        
                        <!-- الرقم الضريبي -->
                        <div class="col-md-6 mb-3">
                            <label for="tax_number" class="form-label">
                                <i class="fas fa-receipt me-1"></i>
                                الرقم الضريبي
                            </label>
                            <input type="text" class="form-control" id="tax_number" name="tax_number" 
                                   placeholder="3xxxxxxxxxx3" maxlength="15">
                            <div class="form-text">الرقم الضريبي السعودي (15 رقم)</div>
                        </div>
                    </div>
                    
                    <!-- العنوان -->
                    <div class="mb-3">
                        <label for="address" class="form-label">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            العنوان
                        </label>
                        <textarea class="form-control" id="address" name="address" rows="3" 
                                  placeholder="العنوان الكامل للعميل"></textarea>
                    </div>
                    
                    <!-- حد الائتمان -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="credit_limit" class="form-label">
                                <i class="fas fa-credit-card me-1"></i>
                                حد الائتمان (ر.س)
                            </label>
                            <input type="number" class="form-control currency-input" 
                                   id="credit_limit" name="credit_limit" 
                                   min="0" step="0.01" value="0">
                            <div class="form-text">الحد الأقصى للمبلغ المستحق على العميل</div>
                        </div>
                        
                        <!-- حالة العميل -->
                        <div class="col-md-6 mb-3">
                            <label for="is_active" class="form-label">
                                <i class="fas fa-toggle-on me-1"></i>
                                حالة العميل
                            </label>
                            <select class="form-select" id="is_active" name="is_active">
                                <option value="1" selected>نشط</option>
                                <option value="0">غير نشط</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- ملاحظات -->
                    <div class="mb-4">
                        <label for="notes" class="form-label">
                            <i class="fas fa-sticky-note me-1"></i>
                            ملاحظات
                        </label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="أي ملاحظات إضافية عن العميل"></textarea>
                    </div>
                    
                    <!-- أزرار الإجراءات -->
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ العميل
                            </button>
                            <button type="button" class="btn btn-success ms-2" id="save-and-new">
                                <i class="fas fa-plus me-2"></i>
                                حفظ وإضافة آخر
                            </button>
                        </div>
                        <div>
                            <a href="{{ url_for('customers') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- الشريط الجانبي -->
    <div class="col-lg-4">
        <!-- نصائح -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-lightbulb me-1"></i>
                    نصائح مفيدة
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        تأكد من صحة البيانات قبل الحفظ
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        الرقم الضريبي اختياري ولكن مهم للشركات
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        يمكنك تعديل المعلومات لاحقاً
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        استخدم حد الائتمان لتتبع المديونية
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- معلومات الرقم الضريبي -->
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-info-circle me-1"></i>
                    الرقم الضريبي السعودي
                </h6>
            </div>
            <div class="card-body">
                <p class="small text-muted mb-2">
                    الرقم الضريبي السعودي يتكون من 15 رقم:
                </p>
                <ul class="small text-muted mb-0">
                    <li>يبدأ بالرقم 3</li>
                    <li>ينتهي بالرقم 3</li>
                    <li>مثال: 300123456789003</li>
                </ul>
                <div class="mt-3">
                    <button type="button" class="btn btn-sm btn-outline-info" id="validate-tax">
                        <i class="fas fa-check me-1"></i>
                        التحقق من الرقم
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// التحقق من صحة النموذج
(function() {
    'use strict';
    
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        
        form.classList.add('was-validated');
    }, false);
})();

// التحقق من صحة الرقم الضريبي
document.getElementById('tax_number').addEventListener('input', function() {
    const taxNumber = this.value;
    const isValid = validateSaudiTaxNumber(taxNumber);
    
    if (taxNumber && !isValid) {
        this.setCustomValidity('الرقم الضريبي غير صحيح');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});

// التحقق من صحة رقم الهاتف
document.getElementById('phone').addEventListener('input', function() {
    const phone = this.value;
    const isValid = validateSaudiPhone(phone);
    
    if (phone && !isValid) {
        this.setCustomValidity('رقم الهاتف غير صحيح');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});

// زر التحقق من الرقم الضريبي
document.getElementById('validate-tax').addEventListener('click', function() {
    const taxNumber = document.getElementById('tax_number').value;
    
    if (!taxNumber) {
        showAlert('يرجى إدخال الرقم الضريبي أولاً', 'warning');
        return;
    }
    
    const isValid = validateSaudiTaxNumber(taxNumber);
    
    if (isValid) {
        showAlert('الرقم الضريبي صحيح', 'success');
    } else {
        showAlert('الرقم الضريبي غير صحيح', 'danger');
    }
});

// زر حفظ وإضافة آخر
document.getElementById('save-and-new').addEventListener('click', function() {
    const form = document.querySelector('.needs-validation');
    
    if (form.checkValidity()) {
        // إضافة حقل مخفي للإشارة إلى الرغبة في إضافة عميل آخر
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'save_and_new';
        input.value = '1';
        form.appendChild(input);
        
        form.submit();
    } else {
        form.classList.add('was-validated');
        showAlert('يرجى تصحيح الأخطاء في النموذج', 'danger');
    }
});

// وظائف التحقق
function validateSaudiTaxNumber(taxNumber) {
    if (!taxNumber) return true; // اختياري
    
    // يجب أن يكون 15 رقم
    if (taxNumber.length !== 15) return false;
    
    // يجب أن يبدأ بـ 3 وينتهي بـ 3
    if (!taxNumber.startsWith('3') || !taxNumber.endsWith('3')) return false;
    
    // يجب أن يحتوي على أرقام فقط
    if (!/^\d+$/.test(taxNumber)) return false;
    
    return true;
}

function validateSaudiPhone(phone) {
    if (!phone) return true; // اختياري
    
    // إزالة المسافات والرموز
    phone = phone.replace(/[\s\-\(\)]/g, '');
    
    // التحقق من الأنماط المقبولة
    const patterns = [
        /^05\d{8}$/,           // 05xxxxxxxx
        /^\+9665\d{8}$/,       // +9665xxxxxxxx
        /^9665\d{8}$/,         // 9665xxxxxxxx
        /^5\d{8}$/             // 5xxxxxxxx
    ];
    
    return patterns.some(pattern => pattern.test(phone));
}

// تنسيق رقم الهاتف تلقائياً
document.getElementById('phone').addEventListener('blur', function() {
    let phone = this.value.replace(/[\s\-\(\)]/g, '');
    
    if (phone && phone.length === 9 && phone.startsWith('5')) {
        this.value = '0' + phone;
    }
});

// تنسيق الرقم الضريبي
document.getElementById('tax_number').addEventListener('input', function() {
    // إزالة جميع الأحرف غير الرقمية
    this.value = this.value.replace(/\D/g, '');
    
    // تحديد الطول إلى 15 رقم
    if (this.value.length > 15) {
        this.value = this.value.substring(0, 15);
    }
});
</script>
{% endblock %}
