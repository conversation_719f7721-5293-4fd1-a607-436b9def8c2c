# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات لنظام محاسبي
Database Models for Mohasabi Accounting System
"""

from datetime import datetime, date
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy import event
import uuid

db = SQLAlchemy()

class User(UserMixin, db.Model):
    """نموذج المستخدمين - Users Model"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    uuid = db.Column(db.String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='viewer')  # admin, accountant, cashier, viewer
    is_active = db.Column(db.Boolean, default=True)
    last_login = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def set_password(self, password):
        """تشفير كلمة المرور"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """التحقق من كلمة المرور"""
        return check_password_hash(self.password_hash, password)
    
    def has_permission(self, permission):
        """التحقق من الصلاحيات"""
        permissions = {
            'admin': ['create', 'read', 'update', 'delete', 'manage_users', 'view_reports'],
            'accountant': ['create', 'read', 'update', 'view_reports'],
            'cashier': ['create', 'read', 'update'],
            'viewer': ['read']
        }
        return permission in permissions.get(self.role, [])
    
    def __repr__(self):
        return f'<User {self.username}>'

class Customer(db.Model):
    """نموذج العملاء - Customers Model"""
    __tablename__ = 'customers'
    
    id = db.Column(db.Integer, primary_key=True)
    uuid = db.Column(db.String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    tax_number = db.Column(db.String(15))  # الرقم الضريبي
    address = db.Column(db.Text)
    credit_limit = db.Column(db.Numeric(15, 2), default=0)
    current_balance = db.Column(db.Numeric(15, 2), default=0)  # الرصيد الحالي
    is_active = db.Column(db.Boolean, default=True)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    invoices = db.relationship('Invoice', backref='customer', lazy=True)
    transactions = db.relationship('Transaction', backref='customer', lazy=True)
    
    def __repr__(self):
        return f'<Customer {self.name}>'

class Supplier(db.Model):
    """نموذج الموردين - Suppliers Model"""
    __tablename__ = 'suppliers'
    
    id = db.Column(db.Integer, primary_key=True)
    uuid = db.Column(db.String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    tax_number = db.Column(db.String(15))
    address = db.Column(db.Text)
    current_balance = db.Column(db.Numeric(15, 2), default=0)
    is_active = db.Column(db.Boolean, default=True)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    expenses = db.relationship('Expense', backref='supplier', lazy=True)
    
    def __repr__(self):
        return f'<Supplier {self.name}>'

class Category(db.Model):
    """نموذج فئات المعاملات - Transaction Categories Model"""
    __tablename__ = 'categories'
    
    id = db.Column(db.Integer, primary_key=True)
    uuid = db.Column(db.String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(100), nullable=False)
    type = db.Column(db.String(20), nullable=False)  # income, expense
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    transactions = db.relationship('Transaction', backref='category', lazy=True)
    expenses = db.relationship('Expense', backref='category', lazy=True)
    
    def __repr__(self):
        return f'<Category {self.name}>'

class PaymentMethod(db.Model):
    """نموذج طرق الدفع - Payment Methods Model"""
    __tablename__ = 'payment_methods'
    
    id = db.Column(db.Integer, primary_key=True)
    uuid = db.Column(db.String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(50), nullable=False)  # نقدي، بنكي، شيك، إلخ
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    transactions = db.relationship('Transaction', backref='payment_method', lazy=True)
    expenses = db.relationship('Expense', backref='payment_method', lazy=True)
    invoice_payments = db.relationship('InvoicePayment', backref='payment_method', lazy=True)
    
    def __repr__(self):
        return f'<PaymentMethod {self.name}>'

class Project(db.Model):
    """نموذج المشاريع - Projects Model"""
    __tablename__ = 'projects'
    
    id = db.Column(db.Integer, primary_key=True)
    uuid = db.Column(db.String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    start_date = db.Column(db.Date)
    end_date = db.Column(db.Date)
    budget = db.Column(db.Numeric(15, 2))
    status = db.Column(db.String(20), default='active')  # active, completed, cancelled
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    transactions = db.relationship('Transaction', backref='project', lazy=True)
    expenses = db.relationship('Expense', backref='project', lazy=True)
    invoices = db.relationship('Invoice', backref='project', lazy=True)
    
    def __repr__(self):
        return f'<Project {self.name}>'

class Invoice(db.Model):
    """نموذج الفواتير - Invoices Model"""
    __tablename__ = 'invoices'

    id = db.Column(db.Integer, primary_key=True)
    uuid = db.Column(db.String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'))
    issue_date = db.Column(db.Date, nullable=False, default=date.today)
    due_date = db.Column(db.Date, nullable=False)

    # المبالغ
    subtotal = db.Column(db.Numeric(15, 2), nullable=False, default=0)
    tax_rate = db.Column(db.Numeric(5, 2), default=15.00)  # ضريبة القيمة المضافة 15%
    tax_amount = db.Column(db.Numeric(15, 2), default=0)
    discount_amount = db.Column(db.Numeric(15, 2), default=0)
    total_amount = db.Column(db.Numeric(15, 2), nullable=False, default=0)
    paid_amount = db.Column(db.Numeric(15, 2), default=0)

    # الحالة والملاحظات
    status = db.Column(db.String(20), default='draft')  # draft, sent, paid, overdue, cancelled
    notes = db.Column(db.Text)
    terms_conditions = db.Column(db.Text)

    # العملة
    currency = db.Column(db.String(3), default='SAR')  # SAR, AED, USD, etc.

    # التواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    sent_at = db.Column(db.DateTime)
    paid_at = db.Column(db.DateTime)

    # العلاقات
    items = db.relationship('InvoiceItem', backref='invoice', lazy=True, cascade='all, delete-orphan')
    payments = db.relationship('InvoicePayment', backref='invoice', lazy=True)

    @property
    def remaining_amount(self):
        """المبلغ المتبقي"""
        return self.total_amount - self.paid_amount

    @property
    def is_overdue(self):
        """هل الفاتورة متأخرة"""
        return self.due_date < date.today() and self.status not in ['paid', 'cancelled']

    def calculate_totals(self):
        """حساب إجمالي الفاتورة"""
        self.subtotal = sum(item.total for item in self.items)
        self.tax_amount = (self.subtotal - self.discount_amount) * (self.tax_rate / 100)
        self.total_amount = self.subtotal - self.discount_amount + self.tax_amount

    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'

class InvoiceItem(db.Model):
    """نموذج عناصر الفاتورة - Invoice Items Model"""
    __tablename__ = 'invoice_items'

    id = db.Column(db.Integer, primary_key=True)
    uuid = db.Column(db.String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)

    description = db.Column(db.String(255), nullable=False)
    quantity = db.Column(db.Numeric(10, 2), nullable=False, default=1)
    unit_price = db.Column(db.Numeric(15, 2), nullable=False)
    total = db.Column(db.Numeric(15, 2), nullable=False)

    # ترتيب العنصر في الفاتورة
    sort_order = db.Column(db.Integer, default=0)

    def calculate_total(self):
        """حساب إجمالي العنصر"""
        self.total = self.quantity * self.unit_price

    def __repr__(self):
        return f'<InvoiceItem {self.description}>'

class InvoicePayment(db.Model):
    """نموذج دفعات الفواتير - Invoice Payments Model"""
    __tablename__ = 'invoice_payments'

    id = db.Column(db.Integer, primary_key=True)
    uuid = db.Column(db.String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)
    payment_method_id = db.Column(db.Integer, db.ForeignKey('payment_methods.id'), nullable=False)

    amount = db.Column(db.Numeric(15, 2), nullable=False)
    payment_date = db.Column(db.Date, nullable=False, default=date.today)
    reference_number = db.Column(db.String(100))  # رقم المرجع أو الشيك
    notes = db.Column(db.Text)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    def __repr__(self):
        return f'<InvoicePayment {self.amount}>'

class Transaction(db.Model):
    """نموذج المعاملات المالية - Financial Transactions Model"""
    __tablename__ = 'transactions'

    id = db.Column(db.Integer, primary_key=True)
    uuid = db.Column(db.String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))

    # معلومات المعاملة
    type = db.Column(db.String(20), nullable=False)  # income, expense
    amount = db.Column(db.Numeric(15, 2), nullable=False)
    description = db.Column(db.String(255), nullable=False)
    transaction_date = db.Column(db.Date, nullable=False, default=date.today)
    reference_number = db.Column(db.String(100))

    # العلاقات
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'))
    category_id = db.Column(db.Integer, db.ForeignKey('categories.id'), nullable=False)
    payment_method_id = db.Column(db.Integer, db.ForeignKey('payment_methods.id'), nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'))

    # العملة والملاحظات
    currency = db.Column(db.String(3), default='SAR')
    notes = db.Column(db.Text)

    # التواريخ والمستخدم
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    def __repr__(self):
        return f'<Transaction {self.description}>'

class Expense(db.Model):
    """نموذج المصروفات - Expenses Model"""
    __tablename__ = 'expenses'

    id = db.Column(db.Integer, primary_key=True)
    uuid = db.Column(db.String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))

    # معلومات المصروف
    description = db.Column(db.String(255), nullable=False)
    amount = db.Column(db.Numeric(15, 2), nullable=False)
    expense_date = db.Column(db.Date, nullable=False, default=date.today)
    receipt_number = db.Column(db.String(100))

    # العلاقات
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'))
    category_id = db.Column(db.Integer, db.ForeignKey('categories.id'), nullable=False)
    payment_method_id = db.Column(db.Integer, db.ForeignKey('payment_methods.id'), nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'))

    # الضرائب والعملة
    tax_amount = db.Column(db.Numeric(15, 2), default=0)
    currency = db.Column(db.String(3), default='SAR')
    notes = db.Column(db.Text)

    # التواريخ والمستخدم
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # العلاقات
    documents = db.relationship('Document', backref='expense', lazy=True)

    def __repr__(self):
        return f'<Expense {self.description}>'

class Document(db.Model):
    """نموذج المستندات - Documents Model"""
    __tablename__ = 'documents'

    id = db.Column(db.Integer, primary_key=True)
    uuid = db.Column(db.String(36), unique=True, nullable=False, default=lambda: str(uuid.uuid4()))

    # معلومات المستند
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer)
    mime_type = db.Column(db.String(100))

    # نوع المستند
    document_type = db.Column(db.String(50))  # invoice, receipt, contract, etc.
    description = db.Column(db.Text)

    # العلاقات
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'))
    expense_id = db.Column(db.Integer, db.ForeignKey('expenses.id'))
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'))
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'))

    # التواريخ والمستخدم
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    uploaded_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    def __repr__(self):
        return f'<Document {self.original_filename}>'

class Setting(db.Model):
    """نموذج الإعدادات - Settings Model"""
    __tablename__ = 'settings'

    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text)
    description = db.Column(db.Text)
    category = db.Column(db.String(50), default='general')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Setting {self.key}>'

class AuditLog(db.Model):
    """نموذج سجل المراجعة - Audit Log Model"""
    __tablename__ = 'audit_logs'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    action = db.Column(db.String(50), nullable=False)  # create, update, delete, login, logout
    table_name = db.Column(db.String(50))
    record_id = db.Column(db.Integer)
    old_values = db.Column(db.Text)  # JSON
    new_values = db.Column(db.Text)  # JSON
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقة مع المستخدم
    user = db.relationship('User', backref='audit_logs')

    def __repr__(self):
        return f'<AuditLog {self.action} by {self.user_id}>'

# Event Listeners لحساب الإجماليات تلقائياً
@event.listens_for(InvoiceItem, 'before_insert')
@event.listens_for(InvoiceItem, 'before_update')
def calculate_invoice_item_total(mapper, connection, target):
    """حساب إجمالي عنصر الفاتورة تلقائياً"""
    target.calculate_total()

@event.listens_for(InvoiceItem, 'after_insert')
@event.listens_for(InvoiceItem, 'after_update')
@event.listens_for(InvoiceItem, 'after_delete')
def update_invoice_totals(mapper, connection, target):
    """تحديث إجماليات الفاتورة عند تغيير العناصر"""
    if target.invoice:
        target.invoice.calculate_totals()

def init_default_data():
    """إنشاء البيانات الافتراضية"""

    # إنشاء المستخدم الافتراضي
    if not User.query.filter_by(username='admin').first():
        admin = User(
            username='admin',
            email='<EMAIL>',
            full_name='مدير النظام',
            role='admin'
        )
        admin.set_password('admin123')
        db.session.add(admin)

    # إنشاء طرق الدفع الافتراضية
    default_payment_methods = [
        {'name': 'نقدي', 'description': 'دفع نقدي'},
        {'name': 'تحويل بنكي', 'description': 'تحويل بنكي'},
        {'name': 'شيك', 'description': 'دفع بالشيك'},
        {'name': 'بطاقة ائتمان', 'description': 'دفع بالبطاقة الائتمانية'},
    ]

    for method_data in default_payment_methods:
        if not PaymentMethod.query.filter_by(name=method_data['name']).first():
            method = PaymentMethod(**method_data)
            db.session.add(method)

    # إنشاء الفئات الافتراضية
    default_categories = [
        {'name': 'مبيعات', 'type': 'income', 'description': 'إيرادات المبيعات'},
        {'name': 'خدمات', 'type': 'income', 'description': 'إيرادات الخدمات'},
        {'name': 'مصروفات تشغيلية', 'type': 'expense', 'description': 'المصروفات التشغيلية'},
        {'name': 'رواتب', 'type': 'expense', 'description': 'رواتب الموظفين'},
        {'name': 'إيجار', 'type': 'expense', 'description': 'إيجار المكتب'},
        {'name': 'مواصلات', 'type': 'expense', 'description': 'مصروفات المواصلات'},
    ]

    for category_data in default_categories:
        if not Category.query.filter_by(name=category_data['name']).first():
            category = Category(**category_data)
            db.session.add(category)

    # إنشاء الإعدادات الافتراضية
    default_settings = [
        {'key': 'company_name', 'value': 'شركتي', 'description': 'اسم الشركة', 'category': 'company'},
        {'key': 'company_address', 'value': '', 'description': 'عنوان الشركة', 'category': 'company'},
        {'key': 'company_phone', 'value': '', 'description': 'هاتف الشركة', 'category': 'company'},
        {'key': 'company_email', 'value': '', 'description': 'بريد الشركة الإلكتروني', 'category': 'company'},
        {'key': 'company_tax_number', 'value': '', 'description': 'الرقم الضريبي للشركة', 'category': 'company'},
        {'key': 'default_currency', 'value': 'SAR', 'description': 'العملة الافتراضية', 'category': 'general'},
        {'key': 'default_tax_rate', 'value': '15.00', 'description': 'معدل الضريبة الافتراضي', 'category': 'general'},
        {'key': 'invoice_prefix', 'value': 'INV-', 'description': 'بادئة رقم الفاتورة', 'category': 'invoices'},
        {'key': 'invoice_terms', 'value': 'شكراً لتعاملكم معنا', 'description': 'شروط الفاتورة الافتراضية', 'category': 'invoices'},
    ]

    for setting_data in default_settings:
        if not Setting.query.filter_by(key=setting_data['key']).first():
            setting = Setting(**setting_data)
            db.session.add(setting)

    db.session.commit()
