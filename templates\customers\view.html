{% extends "base.html" %}

{% block title %}{{ customer.name }} - العملاء - محاسبي{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
        <li class="breadcrumb-item"><a href="{{ url_for('customers') }}">العملاء</a></li>
        <li class="breadcrumb-item active">{{ customer.name }}</li>
    </ol>
</nav>

<!-- Customer Header -->
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <div class="d-flex align-items-center">
        <div class="user-avatar me-3" style="width: 60px; height: 60px; font-size: 1.5rem;">
            {{ customer.name[0] }}
        </div>
        <div>
            <h1 class="h2 mb-0">{{ customer.name }}</h1>
            <p class="text-muted mb-0">
                {% if customer.is_active %}
                    <span class="badge bg-success me-2">نشط</span>
                {% else %}
                    <span class="badge bg-secondary me-2">غير نشط</span>
                {% endif %}
                عميل منذ {{ customer.created_at.strftime('%Y-%m-%d') }}
            </p>
        </div>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-print me-1"></i>
                طباعة
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-download me-1"></i>
                تصدير
            </button>
        </div>
        {% if current_user.has_permission('update') %}
        <div class="btn-group me-2">
            <a href="#" class="btn btn-sm btn-warning">
                <i class="fas fa-edit me-1"></i>
                تعديل
            </a>
        </div>
        {% endif %}
        <div class="btn-group">
            <a href="{{ url_for('add_invoice') }}?customer_id={{ customer.id }}" class="btn btn-sm btn-primary">
                <i class="fas fa-plus me-1"></i>
                فاتورة جديدة
            </a>
        </div>
    </div>
</div>

<!-- Customer Information -->
<div class="row mb-4">
    <!-- Basic Information -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    المعلومات الأساسية
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-5 fw-bold">الاسم:</div>
                    <div class="col-sm-7">{{ customer.name }}</div>
                </div>
                
                {% if customer.email %}
                <div class="row mb-3">
                    <div class="col-sm-5 fw-bold">البريد الإلكتروني:</div>
                    <div class="col-sm-7">
                        <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                            {{ customer.email }}
                        </a>
                    </div>
                </div>
                {% endif %}
                
                {% if customer.phone %}
                <div class="row mb-3">
                    <div class="col-sm-5 fw-bold">الهاتف:</div>
                    <div class="col-sm-7">
                        <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                            {{ customer.phone|phone }}
                        </a>
                    </div>
                </div>
                {% endif %}
                
                {% if customer.tax_number %}
                <div class="row mb-3">
                    <div class="col-sm-5 fw-bold">الرقم الضريبي:</div>
                    <div class="col-sm-7">
                        <code>{{ customer.tax_number }}</code>
                    </div>
                </div>
                {% endif %}
                
                {% if customer.address %}
                <div class="row mb-3">
                    <div class="col-sm-5 fw-bold">العنوان:</div>
                    <div class="col-sm-7">{{ customer.address }}</div>
                </div>
                {% endif %}
                
                {% if customer.credit_limit > 0 %}
                <div class="row mb-3">
                    <div class="col-sm-5 fw-bold">الحد الائتماني:</div>
                    <div class="col-sm-7 text-success">{{ customer.credit_limit|currency }}</div>
                </div>
                {% endif %}
                
                <div class="row mb-3">
                    <div class="col-sm-5 fw-bold">الحالة:</div>
                    <div class="col-sm-7">
                        {% if customer.is_active %}
                            <span class="badge bg-success">نشط</span>
                        {% else %}
                            <span class="badge bg-secondary">غير نشط</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-sm-5 fw-bold">تاريخ الإضافة:</div>
                    <div class="col-sm-7">{{ customer.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Financial Summary -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    الملخص المالي
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-6 fw-bold">إجمالي الفواتير:</div>
                    <div class="col-sm-6">{{ customer.invoices|length }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-6 fw-bold">إجمالي المبيعات:</div>
                    <div class="col-sm-6 text-success">
                        {{ customer.get_total_sales()|currency }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-6 fw-bold">المبلغ المستحق:</div>
                    <div class="col-sm-6 text-warning">
                        {{ customer.get_outstanding_balance()|currency }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-6 fw-bold">آخر فاتورة:</div>
                    <div class="col-sm-6">
                        {% if recent_invoices %}
                            {{ recent_invoices[0].created_at.strftime('%Y-%m-%d') }}
                        {% else %}
                            <span class="text-muted">لا توجد</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-sm-6 fw-bold">متوسط الفاتورة:</div>
                    <div class="col-sm-6">
                        {{ customer.get_average_invoice_amount()|currency }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Notes -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-sticky-note me-2"></i>
                    الملاحظات
                </h5>
            </div>
            <div class="card-body">
                {% if customer.notes %}
                    <p class="mb-0">{{ customer.notes }}</p>
                {% else %}
                    <p class="text-muted mb-0">لا توجد ملاحظات</p>
                {% endif %}
                
                {% if current_user.has_permission('update') %}
                <div class="mt-3">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="editNotes()">
                        <i class="fas fa-edit me-1"></i>
                        تعديل الملاحظات
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Tabs -->
<div class="card">
    <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs" id="customerTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="invoices-tab" data-bs-toggle="tab" 
                        data-bs-target="#invoices" type="button" role="tab">
                    <i class="fas fa-file-invoice me-2"></i>
                    الفواتير ({{ recent_invoices|length }})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="transactions-tab" data-bs-toggle="tab" 
                        data-bs-target="#transactions" type="button" role="tab">
                    <i class="fas fa-exchange-alt me-2"></i>
                    المعاملات ({{ recent_transactions|length }})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="documents-tab" data-bs-toggle="tab" 
                        data-bs-target="#documents" type="button" role="tab">
                    <i class="fas fa-folder me-2"></i>
                    المستندات
                </button>
            </li>
        </ul>
    </div>
    <div class="card-body">
        <div class="tab-content" id="customerTabsContent">
            <!-- Invoices Tab -->
            <div class="tab-pane fade show active" id="invoices" role="tabpanel">
                {% if recent_invoices %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>التاريخ</th>
                                    <th>تاريخ الاستحقاق</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in recent_invoices %}
                                <tr>
                                    <td>
                                        <a href="#" class="text-decoration-none fw-bold">
                                            {{ invoice.invoice_number }}
                                        </a>
                                    </td>
                                    <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else '-' }}</td>
                                    <td>{{ invoice.total_amount|currency }}</td>
                                    <td>
                                        <span class="badge bg-{{ invoice.status|status_badge }}">
                                            {{ get_constants().INVOICE_STATUSES[invoice.status] }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="#" class="btn btn-outline-primary" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="#" class="btn btn-outline-secondary" title="طباعة">
                                                <i class="fas fa-print"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="{{ url_for('invoices') }}?customer_id={{ customer.id }}" class="btn btn-outline-primary">
                            عرض جميع الفواتير
                        </a>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد فواتير</h5>
                        <p class="text-muted">لم يتم إنشاء أي فواتير لهذا العميل بعد</p>
                        <a href="{{ url_for('add_invoice') }}?customer_id={{ customer.id }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            إنشاء فاتورة جديدة
                        </a>
                    </div>
                {% endif %}
            </div>
            
            <!-- Transactions Tab -->
            <div class="tab-pane fade" id="transactions" role="tabpanel">
                {% if recent_transactions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الوصف</th>
                                    <th>النوع</th>
                                    <th>المبلغ</th>
                                    <th>طريقة الدفع</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions %}
                                <tr>
                                    <td>{{ transaction.transaction_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ transaction.description }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if transaction.type == 'income' else 'danger' }}">
                                            {{ get_constants().TRANSACTION_TYPES[transaction.type] }}
                                        </span>
                                    </td>
                                    <td class="{{ 'text-success' if transaction.type == 'income' else 'text-danger' }}">
                                        {{ '+' if transaction.type == 'income' else '-' }}{{ transaction.amount|currency }}
                                    </td>
                                    <td>{{ transaction.payment_method.name if transaction.payment_method else '-' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد معاملات</h5>
                        <p class="text-muted">لم يتم تسجيل أي معاملات لهذا العميل بعد</p>
                    </div>
                {% endif %}
            </div>
            
            <!-- Documents Tab -->
            <div class="tab-pane fade" id="documents" role="tabpanel">
                <div class="text-center py-4">
                    <i class="fas fa-folder fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مستندات</h5>
                    <p class="text-muted">لم يتم رفع أي مستندات لهذا العميل بعد</p>
                    <button type="button" class="btn btn-primary" onclick="uploadDocument()">
                        <i class="fas fa-upload me-1"></i>
                        رفع مستند
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function editNotes() {
        // Implementation for editing notes
        MohasabiApp.showAlert('ميزة تعديل الملاحظات قيد التطوير', 'info');
    }
    
    function uploadDocument() {
        // Implementation for uploading documents
        MohasabiApp.showAlert('ميزة رفع المستندات قيد التطوير', 'info');
    }
</script>
{% endblock %}
